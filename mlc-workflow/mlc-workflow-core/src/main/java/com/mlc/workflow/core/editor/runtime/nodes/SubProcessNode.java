package com.mlc.workflow.core.editor.runtime.nodes;


import io.nop.api.core.annotations.data.DataBean;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 子流程节点 (typeId=16)
 * 用于嵌套子流程
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class SubProcessNode extends FlowNode {

    /**
     * 子流程ID
     */
    private String subProcessId;

    /**
     * 子流程名称
     */
    private String subProcessName;

    public SubProcessNode() {
        this.setTypeId(16);
    }

    /**
     * 验证子流程节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && subProcessId != null && !subProcessId.trim().isEmpty();
    }

}
